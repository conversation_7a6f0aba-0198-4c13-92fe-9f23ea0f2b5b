import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';
import '../main.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // إذا كان هناك اتصال (أي حالة المصادقة قيد التحميل)
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // إذا كان المستخدم مسجل الدخول
        if (snapshot.hasData && snapshot.data != null) {
          // طباعة معلومات لتتبع حالة التسجيل
          print('المستخدم مسجل الدخول: ${snapshot.data?.uid}');
          // استخدام MainNavigation دون إعطاء مفتاح
          return MainNavigation();
        }

        // إذا كان المستخدم غير مسجل الدخول
        print('المستخدم غير مسجل الدخول');
        return const LoginScreen();
      },
    );
  }
}

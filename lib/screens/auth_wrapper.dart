import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';
import '../main.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // إذا كان هناك اتصال (أي حالة المصادقة قيد التحميل)
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // إذا كان المستخدم مسجل الدخول
        if (snapshot.hasData && snapshot.data != null) {
          final user = snapshot.data!;

          // التحقق من تأكيد البريد الإلكتروني
          if (!user.emailVerified) {
            // إذا لم يكن البريد مؤكداً، قم بتسجيل الخروج وإرجاعه لشاشة تسجيل الدخول
            print('المستخدم مسجل الدخول ولكن البريد الإلكتروني غير مؤكد: ${user.uid}');
            // تسجيل خروج تلقائي للمستخدم غير المؤكد مع تأخير لتجنب التداخل مع رسائل login_screen
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              await Future.delayed(Duration(milliseconds: 500));
              await AuthService().signOut();
            });
            return const LoginScreen();
          }

          // إذا كان البريد الإلكتروني مؤكداً، انتقل للتطبيق الرئيسي
          print('المستخدم مسجل الدخول والبريد الإلكتروني مؤكد: ${user.uid}');
          return MainNavigation();
        }

        // إذا كان المستخدم غير مسجل الدخول
        print('المستخدم غير مسجل الدخول');
        return const LoginScreen();
      },
    );
  }
}
